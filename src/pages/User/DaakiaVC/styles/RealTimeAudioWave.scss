// Gradient color variables - change these to update all gradient colors throughout
:root {
  --gradient-color-1: #3B60E4;  // Primary Blue
  --gradient-color-2: #2563EB;  // Dark Blue
  --gradient-color-3: #60A5FA;  // Light Blue
  --gradient-color-4: #1E40AF;  // Deep Blue
  --gradient-color-5: #93C5FD;  // Sky Blue
  
  // RGBA versions for box-shadows and glows
  --gradient-color-1-rgba: 59, 96, 228;  // Primary Blue
  --gradient-color-2-rgba: 37, 99, 235;   // Dark Blue
  --gradient-color-3-rgba: 96, 165, 250;  // Light Blue
  --gradient-color-4-rgba: 30, 64, 175;   // Deep Blue
  --gradient-color-5-rgba: 147, 197, 253; // Sky Blue
}

.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 100%;
  width: 40px;
  position: relative;
  overflow: hidden;

  .static-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--gradient-color-1) 0%, var(--gradient-color-2) 25%, var(--gradient-color-3) 50%, var(--gradient-color-4) 75%, var(--gradient-color-5) 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    opacity: 0.4;
  }

  // Circle that explodes into particles
  .initial-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--gradient-color-1) 0%, var(--gradient-color-2) 25%, var(--gradient-color-3) 50%, var(--gradient-color-4) 75%, var(--gradient-color-5) 100%);
    background-size: 200% 200%;
    animation: explosion-source 2.5s ease-out forwards, gradient-shift 3s ease-in-out infinite;
    z-index: 3;
  }

  // Explosion particles
  &.transitioning {
    // Create three particles that explode outward at specific angles
    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--gradient-color-1) 0%, var(--gradient-color-2) 25%, var(--gradient-color-3) 50%, var(--gradient-color-4) 75%, var(--gradient-color-5) 100%);
      opacity: 0;
      z-index: 2;
    }

    &::before {
      animation: particle-explosion-90deg 2.5s ease-out 0.3s forwards;
    }

    &::after {
      animation: particle-explosion-30deg 2.5s ease-out 0.4s forwards;
    }

    // Third particle (using a pseudo-element on the container)
    .third-particle {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--gradient-color-1) 0%, var(--gradient-color-2) 25%, var(--gradient-color-3) 50%, var(--gradient-color-4) 75%, var(--gradient-color-5) 100%);
      opacity: 0;
      z-index: 2;
      animation: particle-explosion-60deg 2.5s ease-out 0.5s forwards;
    }

    // Main dots that settle into position after returning to center
    .dot {
      opacity: 0;
      transform: scale(0);

      &:nth-child(1) {
        animation: particle-settle-final-1 2.5s ease-out 1.8s forwards;
      }
      &:nth-child(2) {
        animation: particle-settle-final-2 2.5s ease-out 2.0s forwards;
      }
      &:nth-child(3) {
        animation: particle-settle-final-3 2.5s ease-out 2.2s forwards;
      }
    }
  }

  // Override for speaking during transition - dots respond to audio immediately
  &.transitioning.speaking .dot {
    // Enhanced wave motion even during settling animation

    // Add wave motion on top of settling animation
    &:nth-child(1) {
      animation:
        particle-settle-final-1 2.5s ease-out 1.8s forwards,
        quick-wave-motion 0.4s ease-in-out infinite 2.5s;
    }
    &:nth-child(2) {
      animation:
        particle-settle-final-2 2.5s ease-out 2.0s forwards,
        quick-wave-motion 0.4s ease-in-out infinite 2.7s;
    }
    &:nth-child(3) {
      animation:
        particle-settle-final-3 2.5s ease-out 2.2s forwards,
        quick-wave-motion 0.4s ease-in-out infinite 2.9s;
    }
  }

  // Additional sparkle particles (will be added via pseudo-elements on dots)
  &.transitioning .dot {
    &::before {
      content: '';
      position: absolute;
      width: 2px;
      height: 2px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      opacity: 0;
      animation: sparkle-particle 1.2s ease-out forwards;
    }

    &:nth-child(1)::before {
      animation-delay: 2.0s;
      left: -10px;
      top: -8px;
    }
    &:nth-child(2)::before {
      animation-delay: 2.2s;
      left: 8px;
      top: -12px;
    }
    &:nth-child(3)::before {
      animation-delay: 2.4s;
      left: -6px;
      top: 10px;
    }
  }

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;

    // Blue gradient colors
    background: linear-gradient(135deg, var(--gradient-color-1) 0%, var(--gradient-color-2) 25%, var(--gradient-color-3) 50%, var(--gradient-color-4) 75%, var(--gradient-color-5) 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;

    // Different animation delays for each dot
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 1s; }
    &:nth-child(3) { animation-delay: 2s; }
  }

  &.active .dot {
    opacity: 0.8;
    transform: scale(1.2);
  }

  &.speaking .dot {
    opacity: 1;
    // Use vertical scaling based on CSS variables
    transform: scale(var(--dot-scale-x, 1), var(--dot-scale-y, 1));
    animation: dot-vertical-wave 0.6s ease-in-out infinite, gradient-shift 2s ease-in-out infinite;

    // Make them straight lines when scaling vertically
    border-radius: 1px; // Much smaller border-radius for straight lines

    // Different animation delays for each dot to create wave effect
    &:nth-child(1) {
      animation-delay: 0s, 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s, 1s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s, 2s;
    }
  }
}

// Explosion source circle animation
@keyframes explosion-source {
  0% {
    width: 16px;
    height: 16px;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    filter: brightness(1);
  }
  20% {
    transform: translate(-50%, -50%) scale(1.3);
    filter: brightness(1.4);
  }
  30% {
    transform: translate(-50%, -50%) scale(1.6);
    filter: brightness(1.7);
  }
  40% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.6;
    filter: brightness(2.2);
  }
  100% {
    width: 4px;
    height: 4px;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
    filter: brightness(0);
  }
}

// Particle explosion at 90 degrees (straight up)
@keyframes particle-explosion-90deg {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  20% {
    opacity: 0.9;
    transform: translate(-50%, -30px) scale(1.3) rotate(180deg);
  }
  40% {
    opacity: 0.7;
    transform: translate(-50%, -60px) scale(0.9) rotate(360deg);
  }
  60% {
    opacity: 0.5;
    transform: translate(-50%, -90px) scale(1.2) rotate(540deg);
  }
  80% {
    opacity: 0.3;
    transform: translate(-50%, -120px) scale(0.7) rotate(720deg);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -150px) scale(0) rotate(900deg);
  }
}

// Particle explosion at 30 degrees (upper right)
@keyframes particle-explosion-30deg {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  20% {
    opacity: 0.9;
    transform: translate(15px, -25px) scale(1.4) rotate(-180deg);
  }
  40% {
    opacity: 0.7;
    transform: translate(35px, -50px) scale(0.8) rotate(-360deg);
  }
  60% {
    opacity: 0.5;
    transform: translate(55px, -75px) scale(1.3) rotate(-540deg);
  }
  80% {
    opacity: 0.3;
    transform: translate(75px, -100px) scale(0.6) rotate(-720deg);
  }
  100% {
    opacity: 0;
    transform: translate(95px, -125px) scale(0) rotate(-900deg);
  }
}

// Particle explosion at 60 degrees (upper left)
@keyframes particle-explosion-60deg {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  20% {
    opacity: 0.9;
    transform: translate(-35px, -25px) scale(1.2) rotate(180deg);
  }
  40% {
    opacity: 0.7;
    transform: translate(-70px, -50px) scale(0.9) rotate(360deg);
  }
  60% {
    opacity: 0.5;
    transform: translate(-105px, -75px) scale(1.1) rotate(540deg);
  }
  80% {
    opacity: 0.3;
    transform: translate(-140px, -100px) scale(0.7) rotate(720deg);
  }
  100% {
    opacity: 0;
    transform: translate(-175px, -125px) scale(0) rotate(900deg);
  }
}

// Gemini AI inspired gradient animation
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Particle 1 settles into final position with fade-in effect
@keyframes particle-settle-final-1 {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0) rotate(720deg);
  }
  30% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(540deg);
  }
  60% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.1) rotate(360deg);
  }
  80% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.9) rotate(180deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
}

// Particle 2 settles into final position with fade-in effect
@keyframes particle-settle-final-2 {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0) rotate(-720deg);
  }
  30% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(-540deg);
  }
  60% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.1) rotate(-360deg);
  }
  80% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.9) rotate(-180deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
}

// Particle 3 settles into final position with fade-in effect
@keyframes particle-settle-final-3 {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0) rotate(720deg);
  }
  30% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(540deg);
  }
  60% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.1) rotate(360deg);
  }
  80% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.9) rotate(180deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
}

// Sparkle particles that fade away
@keyframes sparkle-particle {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  20% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
  40% {
    opacity: 0.8;
    transform: scale(1) rotate(360deg);
  }
  60% {
    opacity: 0.6;
    transform: scale(1.2) rotate(540deg);
  }
  80% {
    opacity: 0.3;
    transform: scale(0.8) rotate(720deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(900deg);
  }
}

// Enhanced wave motion with Gemini-style fluidity
@keyframes gemini-wave-motion {
  0% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
  25% {
    transform: translateY(-4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  50% {
    transform: translateY(0) scale(calc(var(--dot-scale, 1) * 1.1));
    filter: brightness(1.1);
  }
  75% {
    transform: translateY(4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
}

// Dot vertical wave - stretches dots vertically from center
@keyframes dot-vertical-wave {
  0% {
    transform: scale(var(--dot-scale-x, 1), var(--dot-scale-y, 1));
    filter: brightness(1);
  }
  25% {
    transform: scale(var(--dot-scale-x, 1), calc(var(--dot-scale-y, 1) * 1.3));
    filter: brightness(1.2);
  }
  50% {
    transform: scale(var(--dot-scale-x, 1), calc(var(--dot-scale-y, 1) * 0.7));
    filter: brightness(1.1);
  }
  75% {
    transform: scale(var(--dot-scale-x, 1), calc(var(--dot-scale-y, 1) * 1.5));
    filter: brightness(1.3);
  }
  100% {
    transform: scale(var(--dot-scale-x, 1), var(--dot-scale-y, 1));
    filter: brightness(1);
  }
}

// Quick wave motion for speaking during transition
@keyframes quick-wave-motion {
  0% {
    transform: translateY(0) scale(var(--dot-scale, 1));
  }
  50% {
    transform: translateY(-3px) scale(calc(var(--dot-scale, 1) * 1.15));
  }
  100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
  }
}


